/*
******		FileName	:	message.go
******		Describe	:	此文件主要用于聊天消息数据的管理
******		Date		:	2025-05-10
******		Author		:	TangJinFei
******		Copyright	:	Guangzhou AiYunJi Inc.
******		Note		:   聊天 数据保存与获取的实现
 */

package server

import (
	"ayj_chat_back/api/chat"
	"ayj_chat_back/api/user"
	"ayj_chat_back/internal/consts"
	"ayj_chat_back/internal/dao"
	modelChat "ayj_chat_back/internal/model/chat"
	modelUser "ayj_chat_back/internal/model/user"
	"ayj_chat_back/internal/public/tools"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"gorm.io/gorm"
	"strings"
	"time"
)

// ServerMessage 消息服务结构体
type ServerMessage struct {
	// 分表策略
	shardCount  int8       // 分表数量
	chatManager ServerChat // 聊天长链接
}

// MessageWithReceiver 消息与接收状态的组合结构体（GetMsgListForConvId和GetMsgConvList共用）
type MessageWithReceiver struct {
	// 消息基本信息 (从 ChatMessage 表获取)
	MsgClientId string `gorm:"column:msg_client_id"` //	客户端 消息发送的id
	MsgId       string `gorm:"column:msg_id"`
	MsgType     string `gorm:"column:msg_type"`

	SenderId string      `gorm:"column:sender_id"`
	SendTime *gtime.Time `gorm:"column:send_time"`

	MsgContentFmt  int8   `gorm:"column:msg_content_fmt"`
	MsgContentType string `gorm:"column:msg_content_type"`
	MsgContent     string `gorm:"column:msg_content"`

	// 推送策略信息
	PushStrategy int8   `gorm:"column:push_strategy"`
	PushTargets  string `gorm:"column:push_targets"`

	// 接收状态信息（从 ChatMessageReceiver 表获取）
	ReceiverStatus *int8       `gorm:"column:receiver_status"`
	ReadTime       *gtime.Time `gorm:"column:read_time"`

	// 新增推送策略相关字段
	PushTitle    string `gorm:"column:push_title"`
	PushContent  string `gorm:"column:push_content"`
	PushData     string `gorm:"column:push_data"`
	PushBadge    int    `gorm:"column:push_badge"`
	PushSound    string `gorm:"column:push_sound"`
	PushCategory string `gorm:"column:push_category"`
}

// GetMsgListForConvId 2、会话消息获取
func (s *ServerMessage) GetMsgListForConvId(req *chat.GetMsgListForConvIdReq, userId string) (res *chat.GetMsgListForConvIdRes, err error) {
	// 1. 参数校验
	if req.ConvId == "" {
		return nil, errors.New("会话ID不能为空")
	}

	// 2. 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Size <= 0 || req.Size > 100 {
		req.Size = 100 // 默认每页100条消息
	}

	// 3. 解析会话ID，确定会话类型和目标ID
	var convType int8
	var targetId string
	//	验证用户状态
	if strings.Contains(req.ConvId, "-") {
		// 3.1 单聊会话
		convType = consts.ReceiverType_Private
		parts := strings.Split(req.ConvId, "-")
		if len(parts) != 2 {
			return nil, errors.New("无效的会话ID格式")
		}

		// 确定目标ID（对方ID）
		if parts[0] == userId {
			targetId = parts[1]
		} else if parts[1] == userId {
			targetId = parts[0]
		} else {
			return nil, errors.New("您不是该会话的成员")
		}

		// 验证是否是好友关系
		var friendCount int64
		result := dao.Db.Model(&modelChat.FriendRelation{}).
			Where("user_id = ? AND friend_id = ? AND deleted_at IS NULL", userId, targetId).
			Count(&friendCount)
		if result.Error != nil {
			return nil, fmt.Errorf("验证好友关系失败: %w", result.Error)
		}
		if friendCount == 0 {
			return nil, errors.New("对方不是您的好友，无法查看消息")
		}
	} else {
		// 3.2 群聊会话
		convType = consts.ReceiverType_Group
		targetId = req.ConvId

		// 检查用户是否是群成员，同时获取用户角色
		var memberInfo modelChat.ChatGroupMember
		result := dao.Db.Model(&modelChat.ChatGroupMember{}).
			Select("id, member_role").
			Where("group_id = ? AND user_id = ? AND deleted_at IS NULL AND exit_time IS NULL", targetId, userId).
			First(&memberInfo)

		if result.Error != nil {
			if errors.Is(result.Error, gorm.ErrRecordNotFound) {
				return nil, errors.New("您不是该群组的成员，无法查看消息")
			}
			return nil, fmt.Errorf("验证群成员关系失败: %w", result.Error)
		}
	}

	// 4. 构建查询 - 使用联表查询一次性获取所有需要的数据
	var messagesWithReceiver []MessageWithReceiver
	var queryBuilder *gorm.DB

	if convType == consts.ReceiverType_Private {
		// 4.1 单聊：查询双方之间的消息（修复接收状态查询逻辑）
		queryBuilder = dao.Db.Table("chat_messages as cm").
			Select(`
				cm.msg_client_id, cm.msg_id,cm.msg_type, cm.sender_id, cm.send_time,
				cm.msg_content_fmt, cm.msg_content_type, cm.msg_content,
				cmr.status as receiver_status, cmr.read_time
			`).
			Joins(`LEFT JOIN chat_message_receivers as cmr ON cm.msg_id = cmr.msg_id
				AND cmr.receiver_id = CASE
					WHEN cm.sender_id = ? THEN cm.receiver_id
					ELSE ?
				END`, userId, userId). //	 chat_message_receivers 接收表里面的 消息状态
			Where("((cm.sender_id = ? AND cm.receiver_id = ?) OR (cm.sender_id = ? AND cm.receiver_id = ?)) AND cm.deleted_at IS NULL",
				userId, targetId, targetId, userId) //	chat_messages 双方发送的消息体
	} else {
		// 4.2 群聊：查询群内的消息，包含推送策略和目标
		queryBuilder = dao.Db.Table("chat_messages as cm").
			Select(`
				cm.msg_client_id, cm.msg_id, cm.msg_type, cm.sender_id, cm.send_time,
				cm.msg_content_fmt, cm.msg_content_type, cm.msg_content,
				cm.push_strategy, cm.push_targets,
				cmr.status as receiver_status, cmr.read_time
			`).
			Joins("LEFT JOIN chat_message_receivers as cmr ON cm.msg_id = cmr.msg_id AND cmr.receiver_id = ?", userId).
			Where("cm.receiver_id = ? AND cm.receiver_type = ? AND cm.deleted_at IS NULL",
				targetId, convType)
		
		// 根据用户角色和消息推送策略过滤消息
		// 获取用户在群中的角色
		var memberRole int
		result := dao.Db.Model(&modelChat.ChatGroupMember{}).
			Select("member_role").
			Where("group_id = ? AND user_id = ? AND deleted_at IS NULL AND exit_time IS NULL", targetId, userId).
			Pluck("member_role", &memberRole)
		
		if result.Error == nil {
			// 根据用户角色过滤消息
			switch memberRole {
			case 0: // 普通成员
				// 普通成员只能看到：全部推送、自己发送的、自己是接收者的、自己在推送目标中的消息
				queryBuilder = queryBuilder.Where(`(
					cm.push_strategy = ? OR 
					cm.sender_id = ? OR 
					(cm.push_strategy = ? AND JSON_CONTAINS(cm.push_targets, CONCAT('"', ?, '"')))
				)`, 
				modelChat.PushStrategyAll, userId, modelChat.PushStrategyCustom, userId)
			case 1: // 管理员
				// 管理员可以看到：全部推送、自己发送的、自己是接收者的、自己在推送目标中的、管理员可见的消息
				queryBuilder = queryBuilder.Where(`(
					cm.push_strategy = ? OR 
					cm.push_strategy = ? OR 
					cm.sender_id = ? OR 
					(cm.push_strategy = ? AND JSON_CONTAINS(cm.push_targets, CONCAT('"', ?, '"')))
				)`, 
				modelChat.PushStrategyAll, modelChat.PushStrategyGroupAdmin, userId, modelChat.PushStrategyCustom, userId)
			case 2: // 群主
				// 群主可以看到所有消息，不需要额外过滤
			}
		}
	}

	// 5. 获取消息总数
	var totalCount int64
	countQuery := queryBuilder.Session(&gorm.Session{})
	result := countQuery.Count(&totalCount)
	if result.Error != nil {
		return nil, fmt.Errorf("获取消息总数失败: %w", result.Error)
	}

	// 6. 获取消息列表
	result = queryBuilder.Order("cm.send_time DESC"). // 按发送时间降序
							Offset((req.Page - 1) * req.Size).
							Limit(req.Size).
							Find(&messagesWithReceiver)
	if result.Error != nil {
		return nil, fmt.Errorf("获取消息列表失败: %w", result.Error)
	}

	// 7. 收集发送者ID，用于获取用户信息
	senderIds := make([]string, 0, len(messagesWithReceiver))
	senderIdMap := make(map[string]bool)
	for _, msg := range messagesWithReceiver {
		if !senderIdMap[msg.SenderId] {
			senderIds = append(senderIds, msg.SenderId)
			senderIdMap[msg.SenderId] = true
		}
	}

	// 8. 获取发送者用户信息
	var userInfos []user.UserInfoRes
	if len(senderIds) > 0 {
		result = dao.Db.Model(&modelUser.UserInfo{}).
			Select("user_id, user_nick, user_avatar").
			Where("user_id IN ?", senderIds).
			Find(&userInfos)
		if result.Error != nil {
			return nil, fmt.Errorf("获取用户信息失败: %w", result.Error)
		}
	}

	// 9. 创建用户信息映射，方便查询
	userInfoMap := make(map[string]user.UserInfoRes, len(userInfos))
	for _, userInfo := range userInfos {
		userInfoMap[userInfo.UserId] = userInfo
	}

	// 10. 构建消息列表响应
	messageList := make([]chat.MsgBasicInfo, 0, len(messagesWithReceiver))
	for _, msg := range messagesWithReceiver {
		// 获取群聊消息的接收者状态（仅群聊需要）
		var receiversRead, receiversAll []chat.MessageReceiverInfo
		if convType == consts.ReceiverType_Group {
			// 获取已读接收者
			receiversRead = s.getMessageReceivers(msg.MsgId, consts.MessageStatus_Readed)
			// 获取所有接收者
			receiversAll = s.getMessageReceivers(msg.MsgId, -1) // -1表示获取所有状态
		}

		// 构建消息信息
		messageInfo := s.buildMessageInfo(
			msg.MsgClientId, msg.MsgId, msg.MsgType, msg.SenderId, msg.SendTime,
			msg.MsgContentFmt, msg.MsgContentType, msg.MsgContent,
			msg.ReceiverStatus, msg.ReadTime,
			req.ConvId, convType,
			userId,
			userInfoMap,
			receiversRead,
			receiversAll,
		)

		messageList = append(messageList, messageInfo)
	}

	// 11. 构建最终响应
	res = &chat.GetMsgListForConvIdRes{
		ConvId:      req.ConvId,
		ConvType:    convType,
		MessageList: messageList,
		Page:        req.Page,
		Size:        req.Size,
		Total:       totalCount,
	}

	return res, nil
}
