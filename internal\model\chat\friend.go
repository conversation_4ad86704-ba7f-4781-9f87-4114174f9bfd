/*
******		FileName	:	friend.go
******		Describe	:	此文件主要用于好友管理表
******		Date		:	2025-05-08
******		Author		:	TangJinFei
******		Copyright	:	Guangzhou AiYunJi Inc.
******		Note		:   用户好友的数据库模型
 */

package modelChat

import (
	"github.com/gogf/gf/v2/os/gtime"
	"gorm.io/gorm"
	"time"
)

// 好友关系表 - 考虑到千万级用户，采用分表策略，按用户ID哈希分片 表名可以是 friend_relation_{shard_id}
type FriendRelation struct {
	gorm.Model
	Id             int64  `gorm:"name:id;primaryKey;autoIncrement;unique;comment:表数据id"`               // 主键，唯一，自增
	UserId         string `gorm:"index;size:32;name:user_id;comment:用户唯一id"`                           // 用户ID
	FriendId       string `gorm:"index;size:32;name:friend_id;comment:好友用户id"`                         // 好友ID
	Remark         string `gorm:"name:remark;size:64;comment:好友备注名"`                                   // 好友备注
	RelationStatus int    `gorm:"name:relation_status;default:1;comment:好友关系状态，1正常，2特别关注，3不看他，4不让他看我"` // 好友关系状态
	IsStar         bool   `gorm:"name:is_star;default:false;comment:是否星标好友"`                           // 是否星标好友
	IsTop          bool   `gorm:"name:is_top;default:false;comment:是否置顶好友"`                            // 是否置顶好友

	// 分表字段
	ShardKey int `gorm:"name:shard_key;comment:分片键，用于分表"` // 基于用户ID的哈希值

	CreatedAt *gtime.Time `gorm:"name:created_at;type:timestamp;autoCreateTime;time_format:2006-01-02 15:04:05;comment:创建时间"`
	UpdatedAt *gtime.Time `gorm:"name:updated_at;type:timestamp;autoUpdateTime;default:Null;time_format:2006-01-02 15:04:05;comment:更新时间"`
	DeletedAt *gtime.Time `gorm:"name:deleted_at;type:timestamp;time_format:2006-01-02 15:04:05;comment:删除时间"`
}

// 好友申请表 - 记录好友申请信息
type FriendRequest struct {
	gorm.Model
	Id int64 `gorm:"name:id;primary;auto:yes;unique;comment:表数据id"` // 主键，唯一，自增

	SenderId      string `gorm:"index;size:32;name:sender_id;comment:申请者用户id"`       // 申请者ID
	SenderDelShow bool   `gorm:"name:sender_del_show;default:false;comment:接受者删除显示"` // 申请者删除显示

	ReceiverId      string `gorm:"index;size:32;name:receiver_id;comment:接收者用户id"`       // 接收者ID
	ReceiverDelShow bool   `gorm:"name:receiver_del_show;default:false;comment:接受者删除显示"` // 接受者删除显示

	RequestMsg    string      `gorm:"name:request_msg;size:128;comment:申请消息"`                                       // 申请消息
	RequestSource int         `gorm:"name:request_source;default:1;comment:申请来源，1搜索，2群聊，3名片，4二维码， 5附近的人"`           // 申请来源
	HandleStatus  int         `gorm:"name:handle_status;default:1;comment:处理状态，1未处理，2同意，3拒绝，4忽略"`                   // 处理状态
	HandleMsg     string      `gorm:"name:handle_msg;size:128;comment:处理消息"`                                        // 处理消息
	HandleTime    *gtime.Time `gorm:"name:handle_time;type:timestamp;time_format:2006-01-02 15:04:05;comment:处理时间"` // 处理时间

	// 分表字段
	ShardKey int `gorm:"name:shard_key;comment:分片键，用于分表"` // 基于接收者ID的哈希值

	CreatedAt *gtime.Time `gorm:"name:created_at;type:timestamp;auto:no;time_format:2006-01-02 15:04:05;comment:创建时间"`
	UpdatedAt *gtime.Time `gorm:"name:updated_at;type:timestamp;auto:no;default:Null;time_format:2006-01-02 15:04:05;comment:更新时间"`
	DeletedAt *gtime.Time `gorm:"name:deleted_at;type:timestamp;time_format:2006-01-02 15:04:05;comment:删除时间"`
}

// 好友标签表
type FriendLabel struct {
	gorm.Model

	Id          int64  `gorm:"name:id;primary;auto:yes;unique;comment:表数据id"` // 主键，唯一，自增
	LabelId     string `gorm:"name:label_id;size:32;comment:标签id"`            // 标签id
	UserId      string `gorm:"index;size:32;name:user_id;comment:用户唯一id"`     // 标签归属用户ID
	LabelName   string `gorm:"name:label_name;size:32;comment:分组名称"`          // 标签名称
	LabelOrder  int    `gorm:"name:label_order;default:0;comment:分组排序"`       // 标签排序
	FriendCount int    `gorm:"name:friend_count;default:0;comment:分组成员数"`     // 分组成员数

	CreatedAt *gtime.Time `gorm:"name:created_at;type:timestamp;auto:no;time_format:2006-01-02 15:04:05;comment:创建时间"`
	UpdatedAt *gtime.Time `gorm:"name:updated_at;type:timestamp;auto:no;default:Null;time_format:2006-01-02 15:04:05;comment:更新时间"`
	DeletedAt *gtime.Time `gorm:"name:deleted_at;type:timestamp;time_format:2006-01-02 15:04:05;comment:删除时间"`
}

// 好友关联标签表
type FriendLabelRelation struct {
	gorm.Model

	Id       int64  `gorm:"primaryKey;autoIncrement;unique;comment:表数据id"` // 主键，唯一，自增
	UserId   string `gorm:"index;size:32;name:user_id;comment:用户唯一id"`     // 用户ID
	FriendId string `gorm:"index;size:32;name:friend_id;comment:好友用户id"`   // 好友ID
	LabelId  string `gorm:"index;size:32;name:label_id;comment:标签id"`      // 标签ID

	CreatedAt *gtime.Time `gorm:"name:created_at;type:timestamp;auto:no;time_format:2006-01-02 15:04:05;comment:创建时间"`
	UpdatedAt *gtime.Time `gorm:"name:updated_at;type:timestamp;auto:no;default:Null;time_format:2006-01-02 15:04:05;comment:更新时间"`
	DeletedAt *gtime.Time `gorm:"name:deleted_at;type:timestamp;time_format:2006-01-02 15:04:05;comment:删除时间"`
}

// 黑名单表 - 记录用户拉黑的其他用户
type FriendBlackList struct {
	gorm.Model

	Id          int64  `gorm:"name:id;primary;auto:yes;unique;comment:表数据id"`        // 主键，唯一，自增
	BlockedId   string `gorm:"index;size:32;name:blocked_id;comment:被拉黑用户id"`        // 被拉黑用户ID
	UserId      string `gorm:"index;size:32;name:user_id;comment:用户唯一id"`            // 用户ID
	BlockReason string `gorm:"name:block_reason;size:128;comment:拉黑原因"`              // 拉黑原因
	BlockType   int    `gorm:"name:block_type;default:1;comment:拉黑类型，1完全拉黑，2仅不看朋友圈"` // 拉黑类型

	// 分表字段
	ShardKey int `gorm:"name:shard_key;comment:分片键，用于分表"` // 基于用户ID的哈希值

	CreatedAt *gtime.Time `gorm:"name:created_at;type:timestamp;auto:no;time_format:2006-01-02 15:04:05;comment:创建时间"`
	UpdatedAt *gtime.Time `gorm:"name:updated_at;type:timestamp;auto:no;default:Null;time_format:2006-01-02 15:04:05;comment:更新时间"`
	DeletedAt *gtime.Time `gorm:"name:deleted_at;type:timestamp;time_format:2006-01-02 15:04:05;comment:删除时间"`
}

// 好友互动记录表 - 记录好友间的互动频率，用于智能排序
type FriendInteraction struct {
	gorm.Model

	Id               int64       `gorm:"primaryKey;autoIncrement;unique;comment:表数据id"`     // 主键，唯一，自增
	UserId           string      `gorm:"index;size:32;name:user_id;comment:用户唯一id"`         // 用户ID
	FriendId         string      `gorm:"index;size:32;name:friend_id;comment:好友用户id"`       // 好友ID
	ChatCount        int         `gorm:"name:chat_count;default:0;comment:聊天次数"`            // 聊天次数
	LastChatTime     *gtime.Time `gorm:"name:last_chat_time;type:timestamp;comment:最后聊天时间"` // 最后聊天时间
	InteractionScore float64     `gorm:"name:interaction_score;default:0;comment:互动分数"`     // 互动分数，用于排序

	// 分表字段
	ShardKey int `gorm:"name:shard_key;comment:分片键，用于分表"` // 基于用户ID的哈希值

	CreatedAt *time.Time `gorm:"name:created_at;type:timestamp;auto:no;time_format:2006-01-02 15:04:05;comment:创建时间"`
	UpdatedAt *time.Time `gorm:"name:updated_at;type:timestamp;auto:no;default:Null;time_format:2006-01-02 15:04:05;comment:更新时间"`
}
