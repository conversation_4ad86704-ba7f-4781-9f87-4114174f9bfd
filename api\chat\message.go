/*
******		FileName	:	chat.go
******		Describe	:	此文件主要用于聊天服务接口定义
******		Date		:	2025-04-03
******		Author		:	TangJinFei
******		Copyright	:	Guangzhou AiYunJi Inc.
******		Note		:   聊天服务相关的API定义
 */

package chat

import (
	"github.com/gogf/gf/v2/frame/g"
)

// 1、会话列表-获取请求
type GetMsgConvListReq struct {
	g.Meta `path:"/msg-conv-list" tags:"聊天服务" method:"get" summary:"1、会话列表-获取"`
	// 查询条件
	ConvType int `v:"between:0,2#请输入目标类型|目标类型只能是0-2" default:"0" p:"conv_type" dc:"会话类型, 0:全部;1:单聊;2:群聊"`

	Page int `p:"page" dc:"页码，从1开始"`
	Size int `p:"size" dc:"每页大小，默认20, 最大100"`
}

// 消息会话基本信息
type MsgConvInfo struct {
	//	会话基本信息
	ConvId     string `json:"conv_id" dc:"会话唯一ID, 单聊 双方的id组合 user_id-user_id, 群聊 群id"`
	ConvType   int8   `json:"conv_type" dc:"会话类型，1单聊，2群聊"`
	ConvNick   string `json:"conv_nick" dc:"会话昵称，好友昵称或群昵称"`
	ConvAvatar string `json:"conv_avatar" dc:"会话头像群头像或好友头像"`

	TargetId    string `json:"target_id" dc:"目标ID，单聊为对方用户ID，群聊为群ID"`
	UnreadCount int    `json:"unread_count" dc:"未读消息数"`
	Status      int8   `json:"status" dc:"会话状态，1正常，2置顶，3免打扰，4删除"`

	//	最后一条消息的内容
	LastMsgInfo MsgBasicInfo `json:"last_msg_info"  dc:"最后一条消息信息"`

	LastMsgTimestamp int64 `json:"last_msg_timestamp"  dc:"最后一条消息的时间戳"`
}

// 1、会话列表-获取响应
type GetMsgConvListRes struct {
	g.Meta `mime:"text/html" example:"string"`

	List             []MsgConvInfo `json:"list" dc:"会话列表"`
	TotalUnreadCount int           `json:"total_unread_count" dc:"总未读消息数"`
	HasMore          bool          `json:"has_more" dc:"是否有更多消息"`
}

// 2、会话消息-获取请求
type GetMsgListForConvIdReq struct {
	g.Meta `path:"/msg-list-for-conv-id" tags:"聊天服务" method:"get" summary:"2、会话消息-获取"`

	ConvId string `v:"required#请输入会话id" json:"conv_id" dc:"会话ID; 单聊 双方的id组合 如:user_id-user_id, 群聊即 群id"`

	Page int `p:"page" dc:"页码，从1开始"`
	Size int `p:"size" dc:"每页大小，默认20, 最大100"`
}

// 2、会话消息-获取响应
type GetMsgListForConvIdRes struct {
	g.Meta `mime:"text/html" example:"string"`

	List []MsgBasicInfo `json:"list" dc:"当前会话的消息列表"`

	Count   int  `p:"count" dc:"消息总数"`
	HasMore bool `json:"has_more" dc:"是否有更多消息"`
}

// 3、会话消息-标记已读(消息id)请求
type MarkConvMsgReadReq struct {
	g.Meta `path:"/mark-conv-msg-read" tags:"聊天服务" method:"post" summary:"3、会话消息-标记已读(消息id)"`

	ConvId string   `v:"required#请输入会话id" p:"conv_id" dc:"会话id"`
	MsgIds []string `v:"required#请输入当前会话的消息id列表" p:"msg_ids" dc:"消息ID列表"`
}

// 3、会话消息-标记已读(消息id)响应
type MarkConvMsgReadRes struct {
	g.Meta `mime:"text/html" example:"string"`

	SuccessCount int `json:"success_count" dc:"成功标记数量"`
}

// 4、会话消息-标记已读(全部)请求
type MarkConvAllMsgReadReq struct {
	g.Meta `path:"/mark-conv-all-read" tags:"聊天服务" method:"post" summary:"4、会话消息-标记已读(全部)" description:"会话id不传或为空,所有会话的所有消息标记已读,否则指定会话的所有消息已读"`

	ConvId string `v:"length:0,32#会话id长度最大32位" p:"conv_id" dc:"会话id不传或为空,所有会话的所有消息标记已读"`
}

// 4、会话消息-标记已读(全部)响应
type MarkConvAllMsgReadRes struct {
	g.Meta `mime:"text/html" example:"string"`

	SuccessCount int `json:"success_count" dc:"成功标记数量"`
}

// 5、未读消息数-获取请求
type GetUnreadCountReq struct {
	g.Meta `path:"/unread-count" tags:"聊天服务" method:"get" summary:"5、未读消息数-获取"`

	ConvId   string `p:"conv_id" dc:"会话唯一ID, 不传或为空，返回未读总数，否则指定会话未读消息数"`
	ConvType int    `p:"conv_type" dc:"会话类型，1单聊，2群聊，不传则获取所有类型"`
}

// 获取未读消息数响应
type UnreadCountInfo struct {
	ConvId      string `json:"conv_id" dc:"会话ID（用户ID或群ID）"`
	ConvType    int8   `json:"conv_type" dc:"会话类型，1单聊，2群聊"`
	UnreadCount int    `json:"unread_count" dc:"未读消息数量"`
	LastMsgId   string `json:"last_msg_id" dc:"最后一条消息ID"`
	LastMsgTime string `json:"last_msg_time"  dc:"最后一条消息时间 2006-01-02 15:04:05"`
}

// 5、未读消息数-获取响应
type GetUnreadCountRes struct {
	g.Meta `mime:"text/html" example:"string"`

	List       []UnreadCountInfo `json:"list" dc:"未读消息列表"`
	TotalCount int               `json:"total_count" dc:"总未读消息数"`
}

// 群聊消息接收者状态信息
type MessageReceiverInfo struct {
	ReceiverId     string `json:"receiver_id" dc:"接收者ID"`
	ReceiverNick   string `json:"receiver_nick" dc:"接收者昵称"`
	ReceiverAvatar string `json:"receiver_avatar" dc:"接收者头像"`
	ReadTimestamp  int64  `json:"read_time_stamp"  dc:"已读时间 2006-01-02 15:04:05"`
	Status         int8   `json:"status" dc:"接收状态，1发送中，2已发送，3已送达，4已读"`

	//StatusTips     string `json:"status_tips" dc:"接收状态提示"`
	//ReadTime       string `json:"read_time" dc:"已读时间 2006-01-02 15:04:05"`
}

// 此结构体定义了客户端收到消息细信息，主要用于服务器发送ws 消息
type MsgBasicInfo struct {
	//	消息基本信息
	MsgType string `json:"msg_type" dc:"消息类型: chat 聊天消息, chat_notice 好友通知消息, system 系统信息,  ping 心跳信息"` // 消息类型: chat 聊天消息, system 系统信息, ping 心跳信息

	//	消息发送者信息
	SenderId      string `json:"sender_id" dc:"发送者ID"`
	SenderNick    string `json:"sender_nick" dc:"发送者昵称"`
	SenderAvatar  string `json:"sender_avatar" dc:"发送者头像"`
	SendTimestamp int64  `json:"send_timestamp" dc:"发送时间戳"`

	//	信息内容
	MsgContentFmt  int8        `json:"msg_content_fmt" dc:"内容格式， 1 json，2 protobuf"`
	MsgContentType string      `json:"msg_content_type" dc:"内容类型  text，image，audio，video ,file"` // ws 消息内容类型  text，image，audio，video ,file
	MsgContent     interface{} `json:"msg_content" dc:"内容 聊天内容 (可扩展)"`                           // ws 消息内容 可扩展

	Extra interface{} `json:"extra" dc:"扩展内容"` // ws 扩展消息内容

	//	接受者信息
	ConvId   string `json:"conv_id" dc:"会话id: 单聊：2个用户的id拼起来(user_id-user-id,字符串比较小的在前面)，群聊：group_id"`
	ConvType int8   `json:"conv_type" dc:"会话类型: 1 单聊 ;2 群聊"`
}

// 聊天消息
type ChatMsgInfo struct {
	//	聊天基本信息
	MsgClientId string `json:"msg_client_id" dc:"客户端发送的消息id， 只有回复给发送端的时候需要带，其他情况为空"` // 客户端发送的消息id， 只有回复给发送端的时候需要带，其他情况为空
	MsgId       string `json:"msg_id" dc:"消息唯一id，由服务器生成"`                            // 消息唯一id

	IsMyself bool        `json:"is_myself" default:"false" dc:"此条消息是否是自己发出的，否则是别人发的"`
	MsgInfo  interface{} `json:"info"  dc:"聊天消息信息"`

	//	单聊消息接收状态
	Status        int8   `json:"status" dc:"单聊 消息状态，1发送中，2已发送，3已送达，4已读"`
	StatusTips    string `json:"status_tips" dc:"消息状态提示"`
	ReadTimestamp int64  `json:"read_timestamp" default:"0" dc:"已读时间 2006-01-02 15:04:05"`

	//	群聊接收者状态（仅群聊时有值）
	ReceiversRead []MessageReceiverInfo `json:"receivers_read,omitempty" dc:"群聊接收者 已读列表（仅群聊时返回）"`
	ReceiversAll  []MessageReceiverInfo `json:"receivers_all,omitempty" dc:"群聊接成员 所有列表（仅群聊时返回）"`
}

// 已读消息内容
type ReadMsgInfo struct {
	MsgStatus int8     `json:"msg_status"`
	MsgIds    []string `json:"msg_ids"`
}

// 同意好友申请消息
type AgreeFriendReqInfo struct {
	FriendNick string `json:"friend_nick"`
	FriendId   string `json:"friend_id"`
}

type MessageInfoEx struct {
	//	消息基本信息
	MsgType string `json:"msg_type" dc:"消息类型: chat 聊天消息, chat_notice 好友通知消息, system 系统信息,  ping 心跳信息"` // 消息类型: chat 聊天消息, system 系统信息, ping 心跳信息

	MsgClientId   string `json:"msg_client_id" dc:"客户端发送的消息id， 只有回复给发送端的时候需要带，其他情况为空"` // 客户端发送的消息id， 只有回复给发送端的时候需要带，其他情况为空
	MsgId         string `json:"msg_id" dc:"消息唯一id，由服务器生成"`                            // 消息唯一id
	MsgStatus     int8   `json:"msg_status" dc:"消息状态，1发送中，2已发送，3已送达，4已读"`
	MsgStatusTips string `json:"msg_status_tips" dc:"消息状态提示"`
	MsgReadTime   string `json:"msg_read_time,omitempty"  dc:"已读时间 2006-01-02 15:04:05"`
	IsMyself      bool   `json:"is_myself" default:"false" dc:"此条消息是否是自己发出的，否则是别人发的"`

	//	信息内容
	MsgContentFmt  int8        `json:"msg_content_fmt" dc:"内容格式， 1 json，2 protobuf"`
	MsgContentType string      `json:"msg_content_type" dc:"内容类型  text，image，audio，video ,file"` // ws 消息内容类型  text，image，audio，video ,file
	MsgContent     interface{} `json:"msg_content" dc:"内容 聊天内容 (可扩展)"`                           // ws 消息内容 可扩展

	//	消息发送者信息
	SenderId      string `json:"sender_id" dc:"发送者ID"`
	SenderNick    string `json:"sender_nick" dc:"发送者昵称"`
	SenderAvatar  string `json:"sender_avatar" dc:"发送者头像"`
	SendTimestamp int64  `json:"send_timestamp" dc:"发送时间戳"`

	//	接受者信息
	ConvId   string `json:"conv_id" dc:"会话id: 单聊：2个用户的id拼起来(user_id-user-id,字符串比较小的在前面)，群聊：group_id"`
	ConvType int8   `json:"conv_type" dc:"会话类型: 1 单聊 ;2 群聊"`

	//	群聊接收者状态（仅群聊时有值）
	ReceiversRead []MessageReceiverInfo `json:"receivers_read,omitempty" dc:"群聊接收者 已读列表（仅群聊时返回）"`
	ReceiversAll  []MessageReceiverInfo `json:"receivers_all,omitempty" dc:"群聊接成员 所有列表（仅群聊时返回）"`
}
