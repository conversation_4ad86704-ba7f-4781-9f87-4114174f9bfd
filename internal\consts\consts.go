package consts

type UserLoginType int //	用户登录类型
type UserLoginRole int //	用户登录角色

// 用户登录类型
const (
	LoginType_UnKnow = 0 //	未知登录类型
	LoginType_App    = 1 //	手机端登录
	LoginType_Web    = 2 //	网页端登录
	LoginType_PcApp  = 3 //	电脑端app登录
)

// 用户登录类型对应的字符串
var loginTypMap = map[int]string{
	LoginType_UnKnow: "Login_UnKnow",
	LoginType_App:    "Login_App",
	LoginType_Web:    "Login_Web",
	LoginType_PcApp:  "Login_PcApp",
}

// 获取登录类型字符串
func LoginTypeToString(_nType int) string {
	if _nType >= 0 && _nType < len(loginTypMap) {
		return loginTypMap[_nType]
	}
	return "Login_UnKnow"
}

// 群加入方式
const (
	GroupJoinType_Freedom  = 1 //	自由加入
	GroupJoinType_Validate = 2 //	需要验证
	GroupJoinType_Forbid   = 3 //	禁止加入
)

// 好友处理状态
const (
	FriendHandlrStatus_Pending = 1 //	待处理
	FriendHandlrStatus_agree   = 2 //	同意
	FriendHandlrStatus_refuse  = 3 //	拒绝
	FriendHandlrStatus_ignore  = 4 //	忽略
)

// 接收者类型常量
const (
	ReceiverType_Private = 1 // 单聊
	ReceiverType_Group   = 2 // 群聊
	ReceiverType_Custom  = 3 // 单聊与群聊的自定义组合
)

// 聊天会话状态 1正常，2置顶，3免打扰，4删除
const (
	ChatConvStatus_Normal = 1 //	正常
	ChatConvStatus_Top    = 2 //	置顶
	ChatConvStatus_NoTips = 3 //	免打扰
	ChatConvStatus_Del    = 4 //	删除
)

// 主消息类型常量
const (
	MsgType_Chat       = "chat"        // 普通聊天消息
	MsgType_ChatNotice = "chat_notice" // 通知消息（消息已读, 好友列表更新）
	MsgType_System     = "system"      // 系统消息
)

// 子消息类型
const (
	//	chat 聊天类型
	ConntetType_Text      = "text"       //	文本消息
	ConntetType_Image     = "image"      //	图片
	ConntetType_Audio     = "audio"      //	音频
	ConntetType_Video     = "video"      //	视频
	ConntetType_File      = "file"       //	文件
	ConntetType_VideoCall = "video_call" //	视频通话
	ConntetType_AudioCall = "audio_call" //	音频通话

	//	chat_notice 类型
	ConntetType_AddFriendOk      = "add_friend_ok"      //	添加好友
	ConntetType_MsgStatusUpdate  = "msg_status_update"  //	聊天信息状态更新
	ConntetType_MsgReadUpdate    = "msg_read_notice"    //	已读信息通知
	ConntetType_FriendListUpdate = "friend_list_update" //	好友列表更新，无需显示
)

// 获取消息状态提示
func GetMsgStatusTips(nMsgStatus int8) string {
	msgStatus, ok := MessageStatusTipsMap[nMsgStatus]
	if ok {
		return msgStatus
	}

	return "UnKnow"
}

var MessageStatusTipsMap = map[int8]string{
	MessageStatus_Sending:   "发送中",
	MessageStatus_Sended:    "已发送",
	MessageStatus_Delivered: "已送达,未读",
	MessageStatus_Readed:    "已读",

	MessageStatus_HasDelete:   "您已删除好友",
	MessageStatus_NotFriend:   "对方不是您的好友",
	MessageStatus_BeenBlocked: "您已被对方拉黑",
	MessageStatus_BeenDeleted: "您已被对方删除好友",

	MessageStatus_GroupMuted:     "您已禁言",
	MessageStatus_NotInGroup:     "您不是该群成员",
	MessageStatus_GroupRemove:    "您已被移出该群组",
	MessageStatus_NotGroupMember: "您未加入该群组",
	MessageStatus_GroupExit:      "您已退出该群组",

	MessageStatus_SysError: "系统错误",
}

// 聊天消息状态
const (
	//	正常状态
	MessageStatus_Sending   = 1 //	发送中
	MessageStatus_Sended    = 2 //	已发送
	MessageStatus_Delivered = 3 //	已送达
	MessageStatus_Readed    = 4 //	已读

	//	单聊异常
	MessageStatus_HasDelete   = 11 //	您已删除好友
	MessageStatus_NotFriend   = 12 //	对方不是您的好友
	MessageStatus_BeenBlocked = 13 //	您已被对方拉黑
	MessageStatus_BeenDeleted = 14 //	您已被对方删除好友

	//	群聊异常
	MessageStatus_GroupMuted     = 16 //	您已禁言
	MessageStatus_GroupRemove    = 17 //	您已不是该群成员
	MessageStatus_NotInGroup     = 18 //	您不是该群成员
	MessageStatus_NotGroupMember = 19 //	您未加入该群组
	MessageStatus_GroupExit      = 20 //	您已退出该群组

	//	系统错误
	MessageStatus_SysError = 21 //	系统错误
)

// 聊天群角色
const (
	ChatGroupRole_Comm  = 0 //	普通用户
	ChatGroupRole_Admin = 1 //	管理岗
	ChatGroupRole_Owner = 2 //	群主
)

// 群信息推送类型
const (
	GroupInfo_GroupCreate          = "group_create"           //	群创建 (发送给所有成员)
	GroupInfo_MemberRequest        = "member_request"         //	加群申请 (发送给管理员)
	GroupInfo_MemberRequestOk      = "member_request_ok"      //	加群成功 (发送给所有成员)
	GroupInfo_MemberRequestFailure = "member_request_failure" //	加群失败 (发送给管理员)
	GroupInfo_MemberQuit           = "member_quit"            //	退群 (发送给管理员)
	GroupInfo_MemberRemove         = "member_remove"          //	剔出群 (发送给管理员)
	GroupInfo_MemberRemoved        = "member_removed"         //	被剔出群 (发送给被踢者)
	GroupInfo_AdminUpdate          = "group_admin_update"     //	群管理员变更 (发送给管理员)
	GroupInfo_OwnerTransfer        = "member_owner_transfer"  //	群主转让 (发送给所有成员)
	GroupInfo_MemberMute           = "member_mute"            //	群成员禁言 (发送给所有成员)
	GroupInfo_Disband              = "member_disband"         //	群解散 (发送给所有成员)

	GroupInfo_GroupNameUpdate    = "group_name_update"     //	群名称更新 (发送给所有成员)
	GroupInfo_GroupAvatarUpdate  = "group_avatar_update"   //	群头像更新 (发送给所有成员)
	GroupInfo_GroupNoticeUpdate  = "group_notice_update"   //	群公告更新 (发送给所有成员)
	GroupInfo_GroupAddTypeUpdate = "group_add_type_update" //	群加入方式更新 (发送给所有成员)
	GroupInfo_GroupDescUpdate    = "group_desc_update"     //	群描述更新 (发送给所有成员)

	//	群个人信息修改
	GroupInfo_MemberNickUpdate = "member_nick_update" //	群成员昵称修改,我在本群的昵称,(发送给所有成员)

	//	系统信息子消息类型
	MsgContentType_ChatWsLoginOk = "chat_ws_login_ok"
)

// 消息推送策略
const (
	// 1、基础推送策略
	PushStrategy_All            = 1 // 全部推送（默认）- 单聊双方都推送，群聊所有成员都推送
	PushStrategy_Sender         = 2 // 仅发送方 - 只推送给消息发送者
	PushStrategy_Receiver       = 3 // 仅接收方 - 只推送给消息接收者（单聊）或群成员（群聊，排除发送者）
	PushStrategy_GroupCustom    = 4 // 指定群聊用户 - 推送给PushTargets指定的用户列表
	PushStrategy_ReceiverCustom = 5 // 指定接收方 - 推送给 单聊与群聊的的接收者

	// 2、群聊专用推送策略
	PushStrategy_GroupAdmin = 11 // 群管理员 - 只推送给群管理员（包括群主）
	PushStrategy_GroupOwner = 12 // 群所有者 - 只推送给群主

	// 3、扩展推送策略
	PushStrategy_Online   = 21 // 仅在线用户 - 只推送给当前在线的目标用户
	PushStrategy_Offline  = 22 // 仅离线用户 - 只推送给当前离线的目标用户（用于离线推送）
	PushStrategy_NoSender = 23 // 排除发送者 - 推送给除发送者外的所有目标用户
	// 4、系统消息
	PushStrategy_SystemOnly = 100 // 系统消息 - 特殊系统消息推送策略
)

// 消息推送策略对应的字符串描述
var PushStrategyMap = map[int8]string{
	PushStrategy_All:            "全部推送",
	PushStrategy_Sender:         "仅发送方",
	PushStrategy_Receiver:       "仅接收方",
	PushStrategy_GroupCustom:    "指定群聊用户",
	PushStrategy_ReceiverCustom: "指定接收方",
	PushStrategy_GroupAdmin:     "群管理员",
	PushStrategy_GroupOwner:     "群所有者",
	PushStrategy_Online:         "仅在线用户",
	PushStrategy_Offline:        "仅离线用户",
	PushStrategy_NoSender:       "排除发送者",
	PushStrategy_SystemOnly:     "系统消息",
}

// 获取推送策略描述
func GetPushStrategyDesc(strategy int8) string {
	if desc, ok := PushStrategyMap[strategy]; ok {
		return desc
	}
	return "未知策略"
}
