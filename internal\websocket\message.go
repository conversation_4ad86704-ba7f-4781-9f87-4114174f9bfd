package websocket

type Message struct {
	Type     string      `json:"type"`     // 消息类型: online/offline/private/group
	From     string      `json:"from"`     // 发送者ID
	To       string      `json:"to"`       // 接收者ID/群ID
	Content  string      `json:"content"`  // 消息内容
	SendTime string      `json:"sendTime"` // 发送时间
	Data     interface{} `json:"data"`     // 附加数据
}

const (
	MessageTypeOnline  = "online"  //	在线
	MessageTypeOffline = "offline" //	离线
	MessageTypePrivate = "private" //	私聊
	MessageTypeGroup   = "group"   //	群聊
)
