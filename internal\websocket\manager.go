// websocket/manager.go
package websocket

import "sync"

type ClientMessage struct {
	Client  *Client
	Message []byte
}

type Manager struct {
	Clients    map[*Client]bool
	Broadcast  chan *ClientMessage
	Register   chan *Client
	Unregister chan *Client
	mu         sync.Mutex
}

var Ws<PERSON>anager = Manager{
	Clients:    make(map[*Client]bool),
	Broadcast:  make(chan *ClientMessage),
	Register:   make(chan *Client),
	Unregister: make(chan *Client),
}

func (m *Manager) Start() {
	for {
		select {
		case client := <-m.Register:
			m.mu.Lock()
			m.Clients[client] = true
			m.mu.Unlock()
		case client := <-m.Unregister:
			m.mu.Lock()
			if _, ok := m.Clients[client]; ok {
				close(client.Send)
				delete(m.Clients, client)
			}
			m.mu.Unlock()
		case message := <-m.Broadcast:
			m.mu.Lock()
			for client := range m.Clients {
				select {
				case client.Send <- message.Message:
				default:
					close(client.Send)
					delete(m.Clients, client)
				}
			}
			m.mu.Unlock()
		}
	}
}
