// websocket/client.go
package websocket

import (
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gorilla/websocket"
)

type Client struct {
	Conn     *ghttp.Request
	User     *model.User
	Send     chan []byte
	ClientID string
}

func NewClient(conn *ghttp.Request, user *model.User, clientID string) *Client {
	return &Client{
		Conn:     conn,
		User:     user,
		Send:     make(chan []byte, 256),
		ClientID: clientID,
	}
}

func (c *Client) Read() {
	defer func() {
		Manager.Unregister <- c
		c.Conn.Close()
	}()

	for {
		_, message, err := c.Conn.ReadMessage()
		if err != nil {
			break
		}
		Manager.Broadcast <- &ClientMessage{
			Client:  c,
			Message: message,
		}
	}
}

func (c *Client) Write() {
	defer func() {
		c.Conn.Close()
	}()

	for {
		select {
		case message, ok := <-c.Send:
			if !ok {
				c.Conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}
			c.Conn.WriteMessage(websocket.TextMessage, message)
		}
	}
}
